import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  Output,
  OnChanges,
  SimpleChanges,
  ChangeDetectorRef,
  ViewChild,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { IftaLabelModule } from 'primeng/iftalabel';
import { InputTextModule } from 'primeng/inputtext';
import { NoDataComponent } from '../no-data/no-data';
import { ProductComponent } from '../product/product';
import { TableModule } from 'primeng/table';
import { InputNumberModule } from 'primeng/inputnumber';
import { DividerModule } from 'primeng/divider';
import { ButtonModule } from 'primeng/button';
import { CommonService } from '../../services/common';
import { TypeSenseService } from '../../services/typesense';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { ProgressSpinnerModule } from 'primeng/progressspinner';

@Component({
  selector: 'app-billing',
  standalone: true,
  templateUrl: './billing.html',
  imports: [
    CommonModule,
    FormsModule,
    IftaLabelModule,
    InputTextModule,
    NoDataComponent,
    ProductComponent,
    TableModule,
    InputNumberModule,
    DividerModule,
    ButtonModule,
    AutoCompleteModule,
    ProgressSpinnerModule,
  ],
})
export class BillingComponent implements OnChanges {
  @Input() cartItems: any[] = [];
  @Input() quantityEdit = false;
  @Input() showActions = false;
  @Output() searchEvent = new EventEmitter<{
    searchText: string;
    event: any;
    keyCode: number;
  }>();
  searchText: string = '';
  selectedProduct: any = null;
  products: any[] = [];
  filteredProducts: any[] = [];
  selectedIndex: number = 0;


  // Checkout properties
  isProcessingCheckout = false;

  @ViewChild('searchInput') searchInput: any;
  constructor(
    private commonService: CommonService,
    private typesenseService: TypeSenseService,
    private cdr: ChangeDetectorRef,
  ) {}
  ngOnChanges(changes: SimpleChanges): void {
    console.log(this.cartItems);
  }

  addToCart(product: any) {
    const cartItem = this.cartItems?.find(
      (item: any) => item.id === product.id,
    );
    if (cartItem) {
      cartItem.quantity += 1;
    } else {
      this.cartItems?.push({ ...product, quantity: 1 });
      this.cdr.detectChanges();
    }
    // Clear the search and selection after adding to cart
    this.products = [];
    this.selectedProduct = null;
    this.filteredProducts = [];
  }

  onSearchChange(event: any) {
    const query = event.query;
    if (query && query.length > 2) {
      this.getsearchProducts(query);
    } else {
      this.filteredProducts = [];
    }
  }
  getsearchProducts(query: string) {
    this.typesenseService.searchProducts(query).then((result) => {
      this.filteredProducts = result.products || [];
    });
  }
  removeFromCart(product: any) {
    const index = this.cartItems.findIndex((item) => item.id === product.id);
    if (index > -1) {
      this.cartItems.splice(index, 1);
    }
  }
  updateQuantity(event: any, product: any) {
    const newQuantity = event.value;

    if (newQuantity < 1) {
      // Remove item from cart if quantity is less than 1
      this.removeFromCart(product);
    } else {
      product.quantity = newQuantity;
    }
  }
  getSubTotal() {
    return this.cartItems.reduce(
      (total, item) => total + item.price * item.quantity,
      0,
    );
  }
  getDiscount() {
    return (
      this.cartItems.reduce(
        (total, item) => total + item.price * item.quantity,
        0,
      ) * 0.1
    );
  }
  getGrandTotal() {
    return (
      this.cartItems.reduce(
        (total, item) => total + item.price * item.quantity,
        0,
      ) - this.getDiscount()
    );
  }
  onSearch(event: any) {
    this.searchEvent.emit({
      searchText: event.target.value,
      event,
      keyCode: event.keyCode || 0,
    });
  }
  onClearSearch() {
    this.searchText = '';
  }
  clearCart() {
    this.cartItems = [];
  }

  checkout() {
    if (this.cartItems.length === 0) {
      this.commonService.toast({
        severity: 'warn',
        summary: 'Warning',
        detail: 'Cart is empty. Add items to checkout.',
      });
      return;
    }

    this.isProcessingCheckout = true;
    // Prepare order data
    const customerName = ''; 
    const facilityId = '';  
    const totalAmount = this.getGrandTotal();
    const items = this.cartItems.map(item => ({
      sku: item.sku, // Make sure your cartItems have a 'sku' property
      quantity: item.quantity,
      unit_price: item.price,
      sale_price: item.price, // Adjust if you have a separate sale price
    }));

    const orderData: any = {
      status: 'pending',
      total_amount: totalAmount,
      items: items,
    };

    console.log(orderData);
    this.commonService.post('create_order', orderData).subscribe({
      next: (_response) => {
        this.commonService.toast({
          severity: 'success',
          summary: 'Success',
          detail: `Order created successfully! Total: ₹${orderData.total_amount.toFixed(2)}`,
        });
        this.clearCart();
        this.isProcessingCheckout = false;
      },
      error: (error) => {
        console.error('Order creation error:', error);
        this.commonService.toast({
          severity: 'error',
          summary: 'Error',
          detail: 'Order creation failed. Please try again.',
        });
        this.isProcessingCheckout = false;
      },
    });
  }
}
